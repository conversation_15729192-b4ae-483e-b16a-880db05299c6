/* eslint-disable @typescript-eslint/no-require-imports */
const path = require('path');
const Database = require('better-sqlite3');
const fnx = require('../../../../lib/fnx.js');

const timestamp = () => `[${new Date().toUTCString()}]`;
const logg = exports.logg = (...args) => console.log(timestamp(), ...args)

async function POST(req, res) {
    // Create database connection following the pattern in fnx.js
    const dbPath = path.join(process.cwd(), 'data/fetchmon.db');
    const db = new Database(dbPath);
    try {
        logg('Connected to the database.');
        await fnx.db.init(); // Initialize the database if needed
    } catch (error) {
        logg('Error connecting to the database:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }

    try {
        // Parse the request body
        let configData;
        try {
            configData = typeof req.body === 'string' ? JSON.parse(req.body) : req.body;
        } catch (parseError) {
            logg('Error parsing config data:', parseError);
            res.status(400).json({ error: 'Invalid JSON in request body' });
            return;
        }

        // Validate config data
        if (!configData.websocket || !Array.isArray(configData.pairs2Collect) || !Array.isArray(configData.klineIntervals)) {
            res.status(400).json({ error: 'Invalid config data structure' });
            return;
        }

        // Insert the new configuration into the database
        const insertStmt = db.prepare('INSERT INTO config (dtCreated, config) VALUES (DATETIME(\'now\'), ?)');
        const result = insertStmt.run(JSON.stringify(configData));
        
        logg('Configuration updated successfully with ID:', result.lastInsertRowid);
        
        res.status(200).json({
            message: 'Configuration updated successfully',
            id: result.lastInsertRowid,
            config: configData
        });
    } catch (error) {
        logg('Error updating config:', error);
        res.status(500).json({ error: 'Internal server error' });
    } finally {
        // Close the database connection
        if (db) {
            db.close();
        }
    }
}

// Default export
async function handler(req, res) {
    if (req.method === 'POST') {
        return await POST(req, res);
    } else {
        logg('Method not allowed:', req.method);
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

module.exports = handler;
