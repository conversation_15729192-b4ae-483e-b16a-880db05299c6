
/* eslint-disable @typescript-eslint/no-require-imports */
const path = require('path');
const Database = require('better-sqlite3');
const fnx = require('../../../lib/fnx.js');

const timestamp = () => `[${new Date().toUTCString()}]`;
const logg = exports.logg = (...args) => console.log(timestamp(), ...args)

async function GET(req, res) {
    // Create database connection following the pattern in fnx.js
    const dbPath = path.join(process.cwd(), 'data/fetchmon.db');
    const db = new Database(dbPath);
    try {
        logg('Connected to the database.');
        await fnx.db.init(); // Initialize the database if needed
    } catch (error) {
        logg('Error connecting to the database:', error);
        res.status(500).json({ error: 'Internal server error' });
        return;
    }

    try {
        // Get the last record from config table
        const stmt = db.prepare('SELECT * FROM config ORDER BY id DESC LIMIT 1');
        const result = stmt.get();
        
        if (!result) {
            res.status(404).json({ error: 'No configuration found' });
            return;
        }
        
        // Parse the config JSON string back to object
        const configData = JSON.parse(result.config);
        
        res.status(200).json({
            id: result.id,
            dtCreated: result.dtCreated,
            config: configData
        });
    } catch (error) {
        logg('Error fetching config:', error);
        res.status(500).json({ error: 'Internal server error' });
    } finally {
        // Close the database connection
        if (db) {
            db.close();
        }
    }
}

// Default export
async function handler(req, res) {
    if (req.method === 'GET') {
        return await GET(req, res);
    } else {
        logg('Method not allowed:', req.method);
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

module.exports = handler;
