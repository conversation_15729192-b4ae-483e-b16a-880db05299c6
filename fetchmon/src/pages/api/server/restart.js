/* eslint-disable @typescript-eslint/no-require-imports */
// CommonJS formatına çevirin
const util = require('util');
// const fs = require('fs/promises');
const path = require('path');
const fnx = require('../../../lib/fnx.js'); // fnx.js dosyasını içe aktarın
// execPromise'i tanımlayın
const { exec } = require('child_process');
const execPromise = util.promisify(exec);
// const logFilePath = path.join(process.cwd(), 'restart.log');
// async function logToFile(message) {
//     try {
//         await fs.appendFile(logFilePath, `${new Date().toISOString()} - ${message}\n`);
//     } catch (error) {
//         fnx.logg('Error writing to log file:', error);
//     }
// }
async function killProcessOnPort(port) {
    try {
        await fnx.log2DB(`Attempting to kill process on port ${port}`);
        // Check if lsof is available
        try {
            const { stdout, stderr } = await execPromise('command -v lsof');
            if (stderr) {
                await fnx.log2DB(`lsof not found, assuming no process is running on port ${port}`);
                fnx.logg(`lsof not found, assuming no process is running on port ${port}`);
                return true;
            }
        } catch (e) {
            await fnx.log2DB(`lsof not found, assuming no process is running on port ${port}`);
            fnx.logg(`lsof not found, assuming no process is running on port ${port}`);
            return true;
        }

        let pids = [];
        // Find process ID (PID) on the specified port
        try {
            const { stdout: pidStdout, stderr: pidStderr } = await execPromise(`lsof -t -i :${port}`);
            if (pidStderr) {
                await fnx.log2DB(`no process on port ${port}: ${pidStderr}`);
                fnx.logg(`no process on port ${port}:`, pidStderr);
                return true;
            }
            pids = pidStdout.trim().split('\n').filter(pid => pid).map(Number);

        } catch (e) {
            await fnx.log2DB(`no process on port ${port}: ${e}`);
            fnx.logg(`no process on port ${port}:`);
            return true;
        }

        if (pids.length === 0) {
            await fnx.log2DB(`No process found on port ${port}`);
            fnx.logg(`No process found on port ${port}`);
            return true;
        }

        // Kill the process using its PID
        for (const pid of pids) {
            await fnx.log2DB(`Killing process ${pid}`);
            const { stdout: killStdout, stderr: killStderr } = await execPromise(`kill -9 ${pid}`);
            if (killStderr) {
                await fnx.log2DB(`Error killing process ${pid}: ${killStderr}`);
                fnx.logg(`Error killing process ${pid}:`, killStderr);
                return false;
            }
            fnx.logg(`Killed process ${pid}`);
            await fnx.log2DB(`Killed process ${pid}`);
        }

        return true;
    } catch (error) {
        await fnx.log2DB(`Error killing process: ${error}`);
        fnx.logg('Error killing process:', error);
        return false;
    }
}
async function startNodeProcess() {
    try {
        await fnx.log2DB('Attempting to start node process');
        const projectRoot = process.cwd().split('/').slice(0, -1).join('/');
        const scriptPath = path.join(projectRoot, 'fetchers', 'index.js');

        await fnx.log2DB(`Starting node process with script: ${scriptPath}`);

        const { exec } = require('child_process');

        return new Promise((resolve) => {
            let resolved = false;

            const child = exec(`node ${scriptPath}`, async (error, stdout, stderr) => {
                if (resolved) return; // Zaten yanıt dönüldüyse tekrar dönme

                if (stdout) {
                    fnx.logg(`stdout: ${stdout}`);
                    await fnx.log2DB(`Process stdout: ${stdout}`);
                }
                if (stderr) {
                    fnx.logg(`stderr: ${stderr}`);
                    await fnx.log2DB(`Process stderr: ${stderr}`);
                }
                if (error) {
                    fnx.logg(`Exec error: ${error}`);
                    await fnx.log2DB(`Exec error: ${error.message || error}`);
                    resolved = true;
                    resolve(false);
                }
            });

            // Süreç başarıyla başlatıldı mı kontrolü
            child.on('spawn', async () => {
                if (resolved) return;
                fnx.logg('Process spawned successfully');
                await fnx.log2DB('Process spawned successfully');
                resolved = true;
                resolve(true);
            });

            // Süreç başlatılamazsa
            child.on('error', async (error) => {
                if (resolved) return;
                fnx.logg(`Process spawn error: ${error}`);
                await fnx.log2DB(`Process spawn error: ${error.message || error}`);
                resolved = true;
                resolve(false);
            });

            // Zaman aşımı kontrolü - 3 saniye içinde yanıt gelmezse hata döndür
            setTimeout(async () => {
                if (!resolved) {
                    if (child.pid) {
                        fnx.logg('Process started with PID:', child.pid);
                        await fnx.log2DB(`Process started with PID: ${child.pid}`);
                        resolved = true;
                        resolve(true);
                    } else {
                        fnx.logg('Process failed to start within timeout');
                        await fnx.log2DB('Process failed to start within timeout');
                        resolved = true;
                        resolve(false);
                    }
                }
            }, 3000);
        });
    } catch (e) {
        await fnx.log2DB(`Error starting node process: ${e}`);
        fnx.logg('Error in startNodeProcess:', e);
        return false;
    }
}
// POST fonksiyonu (CommonJS formatında)
async function POST(req, res) {
    if (req.method !== 'POST') {
        fnx.logg('log', 'restart.js', 'Method not allowed:', req.method);
        return res.status(405).json({ error: 'Method Not Allowed!' });
    }

    try {
        await fnx.log2DB('Restart process initiated');
        const port = 3000;

        const killSuccess = await killProcessOnPort(port);
        if (!killSuccess) {
            await fnx.log2DB('Failed to kill process');
            return res.status(500).json({ error: 'Failed to kill process' });
        }
        const startSuccess = await startNodeProcess();
        if (!startSuccess) {
            await fnx.log2DB('Failed to start node process');
            return res.status(500).json({ error: 'Failed to start node process' });
        }

        await fnx.log2DB('Start successful');
        return res.status(200).json({ message: 'Restart successful' });
    } catch (error) {
        await fnx.log2DB(`Restart error: ${error}`);
        fnx.logg('Restart error:', error);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
}

// Default export
async function handler(req, res) {
    if (req.method === 'POST') {
        return await POST(req, res);
    } else {
        fnx.logg('log', 'restart.js', 'Method not allowed:', req.method);
        res.setHeader('Allow', ['POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
}

module.exports = handler;
