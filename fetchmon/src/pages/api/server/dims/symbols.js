/* eslint-disable @typescript-eslint/no-require-imports */
const path = require('path');
const Database = require('better-sqlite3');
const fnx = require('../../../../lib/fnx.js');
const timestamp = () => `[${new Date().toUTCString()}]`;
const logg = (...args) => console.log(timestamp(), ...args);

const dbPath = path.join(process.cwd(), 'data/fetchmon.db');
const db = new Database(dbPath);

// Function to fetch symbols from Binance Futures API
async function fetchSymbolsFromBinance() {
  try {
    const response = await fetch('https://fapi.binance.com/fapi/v1/ticker/24hr');
    if (!response.ok) {
      throw new Error(`Binance API request failed with status ${response.status}`);
    }
    const data = await response.json();
    logg('Fetched symbols from Binance:', JSON.stringify(data, null, 2));

    fnx.log2File(data);
    // Extract unique symbols from the response
    const symbols = [...new Set(data.map(item => item.symbol))];
    
    return {symbols, marketDaily: data};
  } catch (error) {
    logg('Error fetching symbols from Binance:', error);
    throw error;
  }
}

function numero(num, dig = 2) {
    if (num) {
        var resp = parseFloat(num).toLocaleString('tr-TR', {
            minimumFractionDigits: dig,
            maximumFractionDigits: dig
        });
        //console.log('numero', num, resp);
        return resp;
    } else {
        return false;
    }
}

function fnPairList(pairList) {
    let pairs = [...pairList].map(s => {
        return { s: s.symbol, v: numero(s.quoteVolume, 0), vn: parseFloat(s.quoteVolume, 0) }
    }).sort((a, b) => a.vn < b.vn && 1 || -1)
    var unique = [...new Set(pairs)];
    var resp = unique
    return resp;
}


// GET handler
async function GET(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ error: 'Method Not Allowed!' });
    }
    try {
        await fnx.db.initDims(); // Ensure database is initialized
        logg('Connected to the database.');
    }
    catch (error) {
        logg('Error in GET /api/server/dims/symbols:', error);
        return res.status(500).json({ error: 'Internal Server ErrorX' });
    }
    
    const forceUpdate = req.query.forceupdate === 'true';
    const detailed = req.query.detailed === 'true';
    const getWho = detailed ? 'marketdaily' : 'symbols';
    const schema = req.query.schema;
    console.log('Force update:', forceUpdate, 'Detailed:', detailed);

    try {
        // Query database for symbols dimension
        const stmt = db.prepare('SELECT value, dtCreated FROM dims WHERE dim = ? ORDER BY dtCreated DESC LIMIT 1');
        const result = stmt.get(getWho);

        if (result && !forceUpdate) {
            // Return existing data
            let symbols = JSON.parse(result.value);
            let dtCreated = result.dtCreated;
            if (detailed && schema == 'pairlist') {
                symbols = fnPairList(symbols);
            }
            return res.status(200).json({ data: symbols, dtCreated });
        } else {
            // No data in database, fetch from Binance API
            console.log('Returning symbols from source');
            const { symbols, marketDaily } = await fetchSymbolsFromBinance();

            // Save to database
            const insertStmt = db.prepare('INSERT INTO dims (dtCreated, dim, value) VALUES (DATETIME(\'now\'), ?, ?)');
            insertStmt.run('symbols', JSON.stringify(symbols));

            // Save to database
            const insertStmt2 = db.prepare('INSERT INTO dims (dtCreated, dim, value) VALUES (DATETIME(\'now\'), ?, ?)');
            insertStmt2.run('marketdaily', JSON.stringify(marketDaily));

            let marketDailyStg = marketDaily;
            if (detailed && schema == 'pairlist') {
                marketDailyStg = fnPairList(marketDailyStg);
            }

            const response = detailed ? marketDailyStg : symbols ;
            return res.status(200).json({ data: response, dtCreated: new Date().toISOString(), forceUpdated: forceUpdate });
        }
    } catch (error) {
        logg('Error in GET /api/server/dims/symbols:', error);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
}

// POST handler
async function POST(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method Not Allowed!' });
    }

    try {
        await fnx.db.initDims(); // Ensure database is initialized
        logg('Connected to the database.');
    }
    catch (error) {
        logg('Error in GET /api/server/dims/symbols:', error);
        return res.status(500).json({ error: 'Internal Server ErrorX' });
    }
    
    try {
        let symbols;
        logg('Received POST request to update symbols');
        // Parse request body
        try {
            // No data in database, fetch from Binance API
            const { symbols, marketDaily } = await fetchSymbolsFromBinance();

            // Save to database
            const insertStmt = db.prepare('INSERT INTO dims (dtCreated, dim, value) VALUES (DATETIME(\'now\'), ?, ?)');
            insertStmt.run('symbols', JSON.stringify(symbols));

            // Save to database
            const insertStmt2 = db.prepare('INSERT INTO dims (dtCreated, dim, value) VALUES (DATETIME(\'now\'), ?, ?)');
            insertStmt2.run('marketdaily', JSON.stringify(marketDaily));

            return res.status(200).json({ symbols });

        } catch (parseError) {
            return res.status(400).json({ error: 'Invalid JSON in request body' });
        }

        // Validate symbols data
        if (!Array.isArray(symbols)) {
            return res.status(400).json({ error: 'Symbols must be an array' });
        }

        // Update database
        const insertStmt = db.prepare('INSERT INTO dims (dtCreated, dim, value) VALUES (DATETIME(\'now\'), ?, ?)');
        insertStmt.run('symbols', JSON.stringify(symbols));

        return res.status(200).json({ message: 'Symbols updated successfully', symbols });
    } catch (error) {
        logg('Error in POST /api/server/dims/symbols:', error);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
}

// Main handler
async function handler(req, res) {
  if (req.method === 'GET') {
    return await GET(req, res);
  } else if (req.method === 'POST') {
    return await POST(req, res);
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

module.exports = handler;
