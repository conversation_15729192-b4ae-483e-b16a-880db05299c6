
import { NextRequest, NextResponse } from 'next/server';// fetchmon/src/app/api/server/restart/route.js
// import { NextResponse } from 'next/server';
import { exec } from 'child_process';
import util from 'util';
import fs from 'fs/promises'; // Use fs.promises for async file operations
import path from 'path';

const execPromise = util.promisify(exec);

const logFilePath = path.join(process.cwd(), 'restart.log'); // Log file in the project root

async function logToFile(message) {
  try {
    await fs.appendFile(logFilePath, `${new Date().toISOString()} - ${message}\n`);
  } catch (error) {
    console.error('Error writing to log file:', error);
  }
}

async function killProcessOnPort(port) {
    try {
        await logToFile(`Attempting to kill process on port ${port}`);

        // Check if lsof is available
        try {
            const { stdout, stderr } = await execPromise('command -v lsof');
            if (stderr) {
                await logToFile(`lsof not found, assuming no process is running on port ${port}`);
                console.log(`lsof not found, assuming no process is running on port ${port}`);
                return true;
            }
        } catch (e) {
            await logToFile(`lsof not found, assuming no process is running on port ${port}`);
            console.log(`lsof not found, assuming no process is running on port ${port}`);
            return true;
        }

        let pids = [];
        // Find process ID (PID) on the specified port
        try {
            const { stdout: pidStdout, stderr: pidStderr } = await execPromise(`lsof -t -i :${port}`);
            if (pidStderr) {
                await logToFile(`no process on port ${port}: ${pidStderr}`);
                // console.error(`Error finding process on port ${port}:`, pidStderr);
                // If lsof fails, assume no process is running and return true
                return true;
            }
            pids = pidStdout.trim().split('\n').filter(pid => pid).map(Number);
        } catch (e) {
            await logToFile(`no process on port ${port}: `);
            // console.error(`Error finding process on port ${port}:`, e);
            return true;
        }

        if (pids.length === 0) {
            await logToFile(`No process found on port ${port}`);
            console.log(`No process found on port ${port}`);
            return true;
        }

        // Kill the process using its PID
        for (const pid of pids) {
            await logToFile(`Killing process ${pid}`);
            const { stdout: killStdout, stderr: killStderr } = await execPromise(`kill -9 ${pid}`);
            if (killStderr) {
                await logToFile(`Error killing process ${pid}: ${killStderr}`);
                console.error(`Error killing process ${pid}:`, killStderr);
                return false;
            }
            console.log(`Killed process ${pid}`);
            await logToFile(`Killed process ${pid}`);
        }

        return true;
    } catch (error) {
        await logToFile(`Error killing process: ${error}`);
        console.error('Error killing process:', error);
        return false;
    }
}


async function stopNodeProcess() {
    try {
        await logToFile('Attempting to stop node process via terminate endpoint');
        const response = await fetch('http://localhost:3000/server/terminate', { method: 'GET' });

        if (!response.ok) {
            await logToFile(`Terminate endpoint failed with status: ${response.status}`);
            console.error('Terminate endpoint failed:', response.status);
            // Attempt to kill the process on the port if terminate endpoint fails
            const port = 3000;
            const killSuccess = await killProcessOnPort(port);
            if (!killSuccess) {
                await logToFile('Failed to kill process on port after terminate failure');
                console.error('Failed to kill process on port after terminate failure');
                return false;
            }
            return true;
        }

        // const result = await response.json();
        await logToFile(`Terminate endpoint successful`); //: ${JSON.stringify(result)}
        console.log('Terminate endpoint successful:'); //, result
        return true;

    } catch (error) {
        await logToFile(`Error stopping node process: ${error}`);
        console.error('Error stopping node process:', error);
        // Attempt to kill the process on the port if terminate endpoint fails
        const port = 3000;
        const killSuccess = await killProcessOnPort(port);
        if (!killSuccess) {
            await logToFile('Failed to kill process on port after terminate failure');
            console.error('Failed to kill process on port after terminate failure');
            return false;
        }
        return true;
    }
}

async function startNodeProcess() {
    try {
        await logToFile('Attempting to start node process');
        const command = 'node /Users/<USER>/Documents/Mobile/wb/AlgoTrade/a26_1/fetchers/index.js';
        const { stdout, stderr } = await execPromise(command);
        if (stderr) {
            await logToFile(`Error starting node process: ${stderr}`);
            console.error('Error starting node process:', stderr);
            return false;
        }
        await logToFile(`Node process started:\n${stdout}`);
        console.log('Node process started:\n', stdout);
        return true;
    } catch (error) {
        await logToFile(`Error starting node process: ${error}`);
        console.error('Error starting node process:', error);
        return false;
    }
}


export async function POST(request) {
        console.log('Method:', request.method);
    if (request.method !== 'POST') {
        return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 , headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate'
        }});
    }

    try {
        await logToFile('Stop process initiated');
        const stopSuccess = await stopNodeProcess();
        const port = 3000;
        const killSuccess = await killProcessOnPort(port);

        if (!killSuccess) {
            await logToFile('Failed to kill process');
            return NextResponse.json({ error: 'Failed to kill process' }, { status: 500 });
        }


        if (!stopSuccess) {
            await logToFile('Failed to stop node process');
            return NextResponse.json({ error: 'Failed to stop node process' }, { status: 500 });
        }

        await logToFile('Stop successful');
        return NextResponse.json({ message: 'Stop successful' }, { status: 200 });
    } catch (error) {
        await logToFile(`Stop error: ${error}`);
        console.error('Stop error:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
}