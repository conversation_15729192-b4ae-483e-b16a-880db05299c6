import { NextRequest, NextResponse } from 'next/server';
import { getTradingPairData } from '@root/fetchers/db-query-utils';

// Default trading pairs to check (you can modify this list)
const DEFAULT_TRADING_PAIRS = [
  'BTCUSDT',
  'ETHUSDT', 
  'BNBUSDT',
  'ADAUSDT',
  'XRPUSDT',
  'SOLUSDT',
  'DOTUSDT',
  'DOGEUSDT',
  'AVAXUSDT',
  'MATICUSDT'
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbolsParam = searchParams.get('symbols');
    
    // Use provided symbols or default list
    const symbols = symbolsParam 
      ? symbolsParam.split(',').map(s => s.trim().toUpperCase())
      : DEFAULT_TRADING_PAIRS;

    // Fetch data for all symbols in parallel
    const results = await Promise.allSettled(
      symbols.map(async (symbol) => {
        const dbData = await getTradingPairData(symbol.toLowerCase());
        return {
          symbol: symbol.toUpperCase(),
          status: 'active' as const,
          lastUpdate: Date.now(),
          errorCount: 0,
          recordCount: dbData.totalRecords,
          lastRecordTime: dbData.lastRecordTime,
          totalRecords: dbData.totalRecords,
          closedKlineCount: dbData.closedKlineCount
        };
      })
    );

    // Process results
    const tradingPairs = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          symbol: symbols[index],
          status: 'error' as const,
          lastUpdate: Date.now(),
          errorCount: 1,
          recordCount: 0,
          lastRecordTime: null,
          totalRecords: 0,
          closedKlineCount: 0,
          error: result.reason?.message || 'Failed to fetch data'
        };
      }
    });

    return NextResponse.json({
      success: true,
      count: tradingPairs.length,
      tradingPairs
    });
    
  } catch (error) {
    console.error('Error fetching trading pairs data:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch trading pairs data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { symbols } = body;
    
    if (!symbols || !Array.isArray(symbols)) {
      return NextResponse.json(
        { error: 'Symbols array is required in request body' },
        { status: 400 }
      );
    }

    // Fetch data for provided symbols
    const results = await Promise.allSettled(
      symbols.map(async (symbol: string) => {
        const dbData = await getTradingPairData(symbol.toLowerCase());
        return {
          symbol: symbol.toUpperCase(),
          status: 'active' as const,
          lastUpdate: Date.now(),
          errorCount: 0,
          recordCount: dbData.totalRecords,
          lastRecordTime: dbData.lastRecordTime,
          totalRecords: dbData.totalRecords,
          closedKlineCount: dbData.closedKlineCount
        };
      })
    );

    // Process results
    const tradingPairs = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          symbol: symbols[index],
          status: 'error' as const,
          lastUpdate: Date.now(),
          errorCount: 1,
          recordCount: 0,
          lastRecordTime: null,
          totalRecords: 0,
          closedKlineCount: 0,
          error: result.reason?.message || 'Failed to fetch data'
        };
      }
    });

    return NextResponse.json({
      success: true,
      count: tradingPairs.length,
      tradingPairs
    });
    
  } catch (error) {
    console.error('Error fetching trading pairs data:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch trading pairs data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
