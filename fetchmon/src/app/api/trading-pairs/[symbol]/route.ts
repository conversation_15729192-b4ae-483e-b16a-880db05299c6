import { NextRequest, NextResponse } from 'next/server';
import { getTradingPairData } from '@root/fetchers/db-query-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { symbol: string } }
) {
  try {
    const { symbol } = params;
    
    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      );
    }

    // Fetch data from database
    const dbData = await getTradingPairData(symbol);

    // Format response to match TradingPair interface
    const tradingPair = {
      symbol: symbol.toUpperCase(),
      status: 'active' as const, // Default status, could be determined by data freshness
      lastUpdate: Date.now(),
      errorCount: 0, // Could be tracked separately
      recordCount: dbData.totalRecords,
      lastRecordTime: dbData.lastRecordTime,
      totalRecords: dbData.totalRecords,
      closedKlineCount: dbData.closedKlineCount
    };

    return NextResponse.json({
      success: true,
      data: tradingPair
    });
    
  } catch (error) {
    console.error('Error fetching trading pair data:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch trading pair data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
