// Custom hook for fetching system status with polling

import { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';
import {
  SystemHealth,
  SystemStatus,
  MetricsData
} from '@/lib/types';

export const useSystemStatus = () => {
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [metrics, setMetrics] = useState<MetricsData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch all data in parallel
      const [healthData, statusData, metricsData] = await Promise.all([
        apiClient.getHealth(),
        apiClient.getStatus(),
        apiClient.getMetrics()
      ]);
      
      setHealth(healthData);
      setStatus(statusData);
      setMetrics(metricsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Fetch data immediately on mount
    fetchAllData();
  }, []);

  return {
    health,
    status,
    metrics,
    loading,
    error,
    refresh: fetchAllData
  };
};
