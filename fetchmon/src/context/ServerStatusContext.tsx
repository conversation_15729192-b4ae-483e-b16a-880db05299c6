"use client";
import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
type ServerStatusType = 'healthy' | 'unhealthy' | 'error';

interface ServerStatusContextType {
  serverStatus: ServerStatusType;
  serverStatusLoading: boolean;
  loadingMessage: string;
  pingServer: () => Promise<void>;
  startStopServer: (type: 'start' | 'stop') => Promise<void>;
}

const ServerStatusContext = createContext<ServerStatusContextType | undefined>(undefined);

export const ServerStatusProvider = ({ children }: { children: ReactNode }) => {
  const [serverStatusLoading, setServerStatusLoading] = useState(false);
  const [serverStatus, setServerStatus] = useState<ServerStatusType>('error');
  const [loadingMessage, setLoadingMessage] = useState('checking');

  const pingServer = async () => {
    try {
      setServerStatusLoading(true);
      setLoadingMessage('checking');
      const response = await fetch('/api/server/ping');
      if (!response.ok) {
        throw new Error('Ping failed: ' + response.statusText);
      }
      setServerStatus('healthy');
    } catch (error: unknown) {
      setServerStatus('unhealthy');
      console.error('Ping error:', (error as Error).message);
    } finally {
      setServerStatusLoading(false);
    }
  };


  const pingServer_passive = async () => {
    try {
      setLoadingMessage('checking');
      const response = await fetch('/api/server/ping');
      if (!response.ok) {
        throw new Error('Ping failed: ' + response.statusText);
      }
      setServerStatus('healthy');
    } catch (error: unknown) {
      setServerStatus('unhealthy');
    } finally {
    }
  };

  const startStopServer = async (type: 'start' | 'stop') => {
    try {
      setServerStatusLoading(true);
      setLoadingMessage(type === 'start' ? 'starting' : 'stopping');
      const endpoint = type === 'start' ? '/api/server/restart' : '/api/server/stop';

      console.log(`${type} operation started...`);
      const response = await fetch(endpoint, { method: 'POST' });
      if (!response.ok) {
        throw new Error(`${type} failed with status: ${response.status}`);
      }

      console.log(`${type} API call successful`);

      if (type === 'start') {
        setLoadingMessage('waiting for server');
        console.log('Waiting for server to fully start...');
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      await pingServer();
    } catch (error: unknown) {
      console.error(`${type} error:`, error);
      setServerStatusLoading(false);
    }
  };

  useEffect(() => {
    pingServer();
    const interval = setInterval(pingServer_passive, 30000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  return (
    <ServerStatusContext.Provider value={{ serverStatus, serverStatusLoading, loadingMessage, pingServer, startStopServer }}>
      {children}
    </ServerStatusContext.Provider>
  );
};

export const useServerStatus = () => {
  const context = useContext(ServerStatusContext);
  if (context === undefined) {
    throw new Error('useServerStatus must be used within a ServerStatusProvider');
  }
  return context;
};