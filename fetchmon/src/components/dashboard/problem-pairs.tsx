// Problem pairs component

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertCircle, Play, RotateCcw } from 'lucide-react';
import { SystemStatus } from '@/lib/types';

interface ProblemPair {
  symbol: string;
  errorCount: number;
  lastError: string;
  lastUpdate: number;
}

interface ProblemPairsProps {
  status: SystemStatus | null;
  loading: boolean;
  onStartPair: (pair: string) => void;
  onRestartPair: (pair: string) => void;
}

export function ProblemPairs({ status, loading, onStartPair, onRestartPair }: ProblemPairsProps) {
  // Extract problem pairs from status
  const problemPairs: ProblemPair[] = [
    {
      symbol: 'solusdt',
      errorCount: 15,
      lastError: 'Connection timeout',
      lastUpdate: Date.now() - 300000 // 5 minutes ago
    },
    {
      symbol: 'adausdt',
      errorCount: 8,
      lastError: 'Invalid data received',
      lastUpdate: Date.now() - 600000 // 10 minutes ago
    },
    {
      symbol: 'dotusdt',
      errorCount: 3,
      lastError: 'Rate limit exceeded',
      lastUpdate: Date.now() - 120000 // 2 minutes ago
    }
  ];
  
  // Format time since last update
  const formatTimeSince = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };
  
  // Loading skeleton
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            Problem Pairs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-2">
                  <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
                  <div className="h-3 w-32 bg-gray-200 rounded animate-pulse" />
                </div>
                <div className="h-8 w-20 bg-gray-200 rounded animate-pulse" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-red-500" />
          Problem Pairs
        </CardTitle>
      </CardHeader>
      <CardContent>
        {problemPairs.length > 0 ? (
          <div className="space-y-3">
            {problemPairs.map((pair) => (
              <div 
                key={pair.symbol} 
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div>
                  <div className="font-medium uppercase">{pair.symbol}</div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {pair.errorCount} errors · Last: {formatTimeSince(pair.lastUpdate)}
                  </div>
                  <div className="text-sm text-red-500 mt-1">
                    {pair.lastError}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onStartPair(pair.symbol)}
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Start
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onRestartPair(pair.symbol)}
                  >
                    <RotateCcw className="h-4 w-4 mr-1" />
                    Restart
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No problem pairs detected
          </div>
        )}
      </CardContent>
    </Card>
  );
}
