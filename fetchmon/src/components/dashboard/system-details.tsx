// System details component

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  Database, 
  Server, 
  HardDrive,
  Wifi,
  AlertTriangle
} from 'lucide-react';
import { SystemStatus, MetricsData } from '@/lib/types';

interface SystemDetailsProps {
  status: SystemStatus | null;
  metrics: MetricsData | null;
  loading: boolean;
}

export function SystemDetails({ status, metrics, loading }: SystemDetailsProps) {
  // Calculate memory percentage
  const memoryPercentage = metrics?.processMemory 
    ? Math.round((metrics.processMemory.heapUsed / metrics.processMemory.heapTotal) * 100)
    : 0;
  
  // Calculate queue percentages
  const queueTotal = metrics?.tickQueueJobs 
    ? metrics.tickQueueJobs.waiting + metrics.tickQueueJobs.active + metrics.tickQueueJobs.failed
    : 0;
  
  const waitingPercentage = metrics?.tickQueueJobs && queueTotal > 0
    ? Math.round((metrics.tickQueueJobs.waiting / queueTotal) * 100)
    : 0;
  
  const activePercentage = metrics?.tickQueueJobs && queueTotal > 0
    ? Math.round((metrics.tickQueueJobs.active / queueTotal) * 100)
    : 0;
  
  const failedPercentage = metrics?.tickQueueJobs && queueTotal > 0
    ? Math.round((metrics.tickQueueJobs.failed / queueTotal) * 100)
    : 0;
  
  // Get Redis connection status
  const redisConnected = status?.system?.redis?.connected || false;
  
  // Get connection details
  const connections = status?.system?.tickCollector 
    ? Object.values(status.system.tickCollector)
    : [];
  
  // Loading skeleton
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>System Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Redis Status */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="h-2 w-full bg-gray-200 rounded animate-pulse" />
          </div>
          
          {/* Memory Usage */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="h-2 w-full bg-gray-200 rounded animate-pulse" />
          </div>
          
          {/* Queue Load */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="h-4 w-28 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="space-y-1">
              <div className="h-2 w-full bg-gray-200 rounded animate-pulse" />
              <div className="h-2 w-full bg-gray-200 rounded animate-pulse" />
              <div className="h-2 w-full bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
          
          {/* Connections */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="space-y-2">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-2 border rounded">
                  <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
                  <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>System Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Redis Status */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              <span className="font-medium">Redis Status</span>
            </div>
            <span className={redisConnected ? "text-green-600" : "text-red-600"}>
              {redisConnected ? "Connected" : "Disconnected"}
            </span>
          </div>
          {!redisConnected && (
            <div className="flex items-center gap-2 text-sm text-red-600">
              <AlertTriangle className="h-4 w-4" />
              <span>Redis connection failed</span>
            </div>
          )}
        </div>
        
        {/* Memory Usage */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              <span className="font-medium">Memory Usage</span>
            </div>
            <span>{memoryPercentage}%</span>
          </div>
          <Progress value={memoryPercentage} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>
              {((metrics?.processMemory?.heapUsed || 0) / 1024 / 1024).toFixed(1)} MB
            </span>
            <span>
              {((metrics?.processMemory?.heapTotal || 0) / 1024 / 1024).toFixed(1)} MB
            </span>
          </div>
        </div>
        
        {/* Queue Load */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              <span className="font-medium">Queue Load</span>
            </div>
            <span>{queueTotal} jobs</span>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <span className="text-xs w-16">Waiting</span>
              <Progress value={waitingPercentage} className="h-2 flex-1" />
              <span className="text-xs w-8">{metrics?.tickQueueJobs?.waiting || 0}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs w-16">Active</span>
              <Progress value={activePercentage} className="h-2 flex-1" />
              <span className="text-xs w-8">{metrics?.tickQueueJobs?.active || 0}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs w-16">Failed</span>
              <Progress value={failedPercentage} className="h-2 flex-1" />
              <span className="text-xs w-8">{metrics?.tickQueueJobs?.failed || 0}</span>
            </div>
          </div>
        </div>
        
        {/* Connections */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Wifi className="h-4 w-4" />
              <span className="font-medium">Connections</span>
            </div>
            <span>{connections.filter(c => c.isConnected).length}/{connections.length} active</span>
          </div>
          <div className="space-y-2">
            {connections.map((connection) => (
              <div 
                key={connection.id} 
                className="flex items-center justify-between p-2 border rounded"
              >
                <div className="flex items-center gap-2">
                  <span className="font-mono text-sm">#{connection.id}</span>
                  <span className="text-xs text-muted-foreground">
                    {connection.url.split('/').pop()?.substring(0, 20) || 'Unknown'}
                  </span>
                </div>
                <span className={connection.isConnected ? "text-green-600 text-sm" : "text-red-600 text-sm"}>
                  {connection.isConnected ? "Active" : "Inactive"}
                </span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
