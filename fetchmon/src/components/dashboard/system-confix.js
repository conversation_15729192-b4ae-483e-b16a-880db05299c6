/* eslint-disable @typescript-eslint/no-unused-expressions */
import { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

import { Progress } from '@/components/ui/progress';
import { useServerStatus } from '@/context/ServerStatusContext';
import { Settings, Edit } from 'lucide-react';
import {
  Server,
  Database,
  Activity,
  HardDrive,
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

export default function SystemConfig(props) {
    const { serverStatus, serverStatusLoading, loadingMessage, pingServer, startStopServer } = useServerStatus();

    const [battleParams, setbattleParamsFN] = useState({});

    const setParameters = val => {
      let currV = JSON.parse(JSON.stringify(battleParams));
      let nVal = {
        ...currV,
      };
      let {fname, fvar} = val;
      nVal[fname] = fvar;
      console.log('set backtestParams', battleParams, nVal)
      setbattleParamsFN(nVal); 
    }

    const handleEdit = () => {
        // Logic to handle edit action, e.g., open a modal or redirect to config page
        console.log('Edit configuration clicked');
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Settings className="h-5 w-5" />
                        System Configuration
                    </div>
                    <Button variant="outline" size="sm" onClick={handleEdit}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                    </Button>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="flex items-start justify-start mb-2">
                    <div className="border-b min-h-[37px] min-w-[150px] w-[150px] pb-1">
                        <span className="text-sm font-medium block">Kline Interval</span>
                    </div>

                    <div className="border-b border-divider min-h-[35px] ml-2 w-full pb-1">
                        <Intervals {...props} candleCounts={true} callbackFN={setParameters} />
                    </div>
                </div>

                <div className="flex items-start justify-start mb-2">
                    <div className="border-b min-h-[37px] min-w-[150px] w-[150px] pb-1">
                        <span className="text-sm font-medium block">Symbols</span>
                    </div>

                    <div className="border-b border-divider min-h-[35px] ml-2 w-full pb-1">
                        <Pairs {...props} callbackFN={setParameters} />
                    </div>
                </div>

                <div className="space-y-4">
                    <div>
                        <h3 className="font-medium mb-2">WebSocket Configuration</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Pairs per Connection:</span>
                                <span>25</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Health Check Interval:</span>
                                <span>10000ms</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Reconnect Interval:</span>
                                <span>5000ms</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Max Reconnect Attempts:</span>
                                <span>3</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Kline Interval:</span>
                                <span>1m</span>
                            </div>
                        </div>
                    </div>
                </div>
{/* 
                            <div className="flex justify-between txt-sm mt-4">
                                <span className="text-muted-foreground">parameters:</span>
                                <span>{JSON.stringify(battleParams, null, 2) }</span>
                            </div> */}

                
            </CardContent>
        </Card>
    );
} 





const Intervals = props => {
  const tagsData = ["1m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "12h", "1d"];
  const [selectedTags, setselectedTags] = useState([]);
  const [candles, setCandles] = useState(100);

  const handleChange = (tag, checked) => {
    if (props.multi) {
      const nextSelectedTags = checked ? [...selectedTags, tag] : selectedTags.filter(t => t !== tag);
      const newA = [...new Set(nextSelectedTags)];
      setselectedTags(newA);
      props?.callbackFN && props?.callbackFN({ fname: 'intervals', fvar: newA });
    } else {
      const nextSelectedTags = checked ? [tag] : selectedTags.filter(t => t !== tag);
      setselectedTags(nextSelectedTags);
      props?.callbackFN && props?.callbackFN({ fname: 'battleInterval', fvar: nextSelectedTags });
    }
  };

  const handleCandle = (vals) => {
    let nVal = vals?.target?.value ? parseFloat(vals?.target.value) : vals;
    setCandles(nVal);
    props.candleCounts && props?.callbackFN && props.callbackFN({ fname: 'candleCounts', fvar: nVal });
  };

  useEffect(() => {
    if (props.battleParams && (props.battleParams.intervals || props.battleParams.battleInterval || props.battleParams.candleCounts)) {
      let parami = props.multi ? props.battleParams.intervals : props.battleParams.battleInterval;
      let bars = props.battleParams && props.battleParams.candleCounts;
      bars && candles !== bars && setCandles(bars);
      parami && JSON.stringify(parami) !== JSON.stringify(selectedTags) && setselectedTags(parami);
    }
  }, [props.battleParams]);

  return (
    <div className="flex flex-row items-start justify-start mr-4">
      {tagsData.map(tag => (
        <button
          key={tag}
          onClick={() => handleChange(tag, !(Array.isArray(selectedTags) && selectedTags.includes(tag)))}
          className={`px-3 py-1 text-sm rounded-md border mr-1 transition-colors ${
            Array.isArray(selectedTags) && selectedTags.includes(tag)
              ? 'bg-primary text-primary-foreground border-primary'
              : 'bg-background text-foreground border-border hover:bg-accent'
          }`}
        >
          {tag}
        </button>
      ))}

      {props.candleCounts && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="ml-1">
                <Input
                  id="candleQty"
                  type="number"
                  value={candles}
                  onChange={handleCandle}
                  className="w-20 h-8 text-sm"
                />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Number of Candles for Indicator calculations</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
};

// export default CardParamsTimes;


const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));

function BinanceFuturesDaily({pair, forceupdate = false} = {}) {
  return new Promise(async (resolve) => {
    try {
      var uri = '/api/server/dims/symbols?detailed=true&schema=pairlist'
      uri += pair ? '&pair=' + pair : '' 
      uri += forceupdate ? '&forceupdate=true' : '';
      console.log(uri)
      const data = await fetcher(uri)  
      resolve(data)
    } catch(e) {
      console.log('fetch err', e)
    }
  });
}

function useStickyState(defaultValue, key) {
  const [value, setValue] = useState(defaultValue);
  // console.log('useStickyState', defaultValue, key)
  useEffect(() => {
    const stickyValue = window.localStorage.getItem(key);
    if (stickyValue && stickyValue !== 'null') {
      // console.log('stickyValue2', JSON.parse(stickyValue))
      try {setValue(JSON.parse(stickyValue));} catch (e) {
        console.log('battle pairs error stickyValue', stickyValue, e)
      }
    } else {
      const fData = async () => {
        // console.log('fData call binance')
        var dt = await BinanceFuturesDaily();
        // console.log('fData JSON.stringify(data)',  JSON.stringify(dt.data))
        window.localStorage.setItem(key, JSON.stringify(dt));
        setValue(JSON.parse(JSON.stringify(dt)));
      }
      fData();
    }
  }, [key]);

  useEffect(() => {
    window.localStorage.setItem(key, JSON.stringify(value));
  }, [key, value]);

  return [value, setValue];
}

const Pairs = props => {
  const { user = {} } = props;
  const { login } = user; 
  const [futuresDaily, setFuturesDaily] = useState(false);
  const [userFavorites, setUserFavorites] = useState(false);
  const [selectedPairs, setselectedPairs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isOpened, setisOpened] = useState(true);
  const [lastpair, setlastpair] = useState(false);
  const [mode, setMode] = useStickyState(null, 'bPairs');
  const [minVolume, setMinVolume] = useState('');

  const handleChange = (tag, checked) => {
    // Always set the last pair to the current tag being interacted with
    setlastpair(tag);

    let nextSelectedTags;
    if (checked) {
      // Check if tag already exists to avoid duplicates
      if (!selectedPairs.includes(tag)) {
        nextSelectedTags = [...selectedPairs, tag];
      } else {
        nextSelectedTags = selectedPairs; // No change needed
      }
    } else {
      // Use findIndex for better performance than filter for single item removal
      const index = selectedPairs.indexOf(tag);
      if (index > -1) {
        nextSelectedTags = [
          ...selectedPairs.slice(0, index),
          ...selectedPairs.slice(index + 1)
        ];
      } else {
        nextSelectedTags = selectedPairs; // No change needed
      }
    }

    // Only update state if there's an actual change
    if (nextSelectedTags !== selectedPairs) {
      setselectedPairs(nextSelectedTags);

      // Only execute callback if mode is truthy
      if (mode) {
        props.callbackFN({ fname: 'pairs', fvar: nextSelectedTags });
        // Update lastpair to the last item in the array if adding, or keep current tag if removing
        if (nextSelectedTags.length > 0) {
          setlastpair(checked ? nextSelectedTags[nextSelectedTags.length - 1] : tag);
        }
      }
    }
  };

  useEffect(() => {
    if (!mode) {
      setLoading(true);
    } else {
      setFuturesDaily(mode?.data);
      setLoading(false);
    }
  }, [mode]);

  useEffect(() => {
    if (props.battleParams) {
      let parami = props.battleParams.pairs;
      // Use a more efficient comparison to avoid unnecessary JSON.stringify calls
      if (parami && (!selectedPairs || parami.length !== selectedPairs.length ||
          !parami.every((pair, index) => pair === selectedPairs[index]))) {
        setselectedPairs(parami);
      }
    } else {
      console.log('no default for pairs');
    }
  }, [props.battleParams, selectedPairs]);

  // Memoize the pairs array to avoid recalculating on every render
  const allPairs = useMemo(() => {
    return Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [];
  }, [futuresDaily]);

  // Memoize the selected pairs count for performance
  const selectedCount = useMemo(() => {
    return Array.isArray(selectedPairs) ? selectedPairs.length : 0;
  }, [selectedPairs]);

  const refresh = async () => {
    setLoading(true);
    try {
      setFuturesDaily([]);
      var dt = await BinanceFuturesDaily({forceupdate: true});
      setFuturesDaily(dt?.data);
      setMode(dt);
      setLoading(false);
    } catch (error) {
      console.error('Error refreshing pairs data:', error);
      setLoading(false);
    }
  };

  const selectA = useCallback(() => {
    // Early return if no data
    if (allPairs.length === 0) {
      setselectedPairs([]);
      mode && props.callbackFN({ fname: 'pairs', fvar: [] });
      mode && setlastpair(null);
      return;
    }

    // Use memoized pairs array
    setselectedPairs(allPairs);

    // Only execute callback and lastpair logic if mode is truthy
    if (mode) {
      props.callbackFN({ fname: 'pairs', fvar: allPairs });
      // Optimize last pair selection - avoid slice and pop
      if (allPairs.length > 0) {
        setlastpair(allPairs[allPairs.length - 1]);
      }
    }
  }, [allPairs, mode, props]);

  const setRandomPairs = useCallback((n) => {
    // Early return if no data
    if (allPairs.length === 0) {
      setselectedPairs([]);
      mode && props.callbackFN({ fname: 'pairs', fvar: [] });
      mode && setlastpair(null);
      return;
    }

    // Use Fisher-Yates shuffle for better performance than sort()
    const shuffled = [...allPairs];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }

    const selected = shuffled.slice(0, n);
    setselectedPairs(selected);

    // Only execute callback and lastpair logic if mode is truthy
    if (mode) {
      props.callbackFN({ fname: 'pairs', fvar: selected });
      // Optimize last pair selection - avoid slice and pop
      if (selected.length > 0) {
        setlastpair(selected[selected.length - 1]);
      }
    }
  }, [allPairs, mode, props]);

  const fnSelectMinVolume = (vals) => {
    setMinVolume(vals?.target.value ? parseFloat(vals?.target.value) : '');
  };

  const fnSetMinVolumeFilter = () => {
    // Early return if no data or invalid minVolume
    if (!Array.isArray(futuresDaily) || futuresDaily.length === 0 || !minVolume) {
      setselectedPairs([]);
      mode && props.callbackFN({ fname: 'pairs', fvar: [] });
      mode && setlastpair(null);
      return;
    }

    // Pre-calculate threshold once
    const volumeThreshold = parseFloat(minVolume) * 1000000;

    // Single pass filter and map operation
    const array = futuresDaily
      .filter(p => p.vn > volumeThreshold)
      .map(p => p.s);

    setselectedPairs(array);

    // Only execute callback and lastpair logic if mode is truthy
    if (mode) {
      props.callbackFN({ fname: 'pairs', fvar: array });
      // Optimize last pair selection - avoid slice and pop
      if (array.length > 0) {
        setlastpair(array[array.length - 1]);
      }
    }
  };

  const openlink = () => {
    var urlX = 'https://www.tradingview.com/chart?symbol=BINANCE%3A' + lastpair + 'PERP';
    console.log('', new Date().toISOString(), lastpair);
    lastpair && window.open(urlX, "_blank");
  };

  const Favoriler = []; // Bu değişkenin nereden geldiğini belirtmediniz, gerekliyse tanımlayın

  return (
    <div className="w-full">
      <div className="flex flex-row items-start justify-start pb-1 mr-4 flex-wrap gap-1 w-full">
        <Button 
          size="sm" 
          variant="default"
          className="h-7 px-2 text-xs rounded-md bg-blue-500 hover:bg-blue-600 text-white"
          onClick={() => refresh()}
        >
          Tazele
        </Button>

        {loading && <Skeleton className="h-7 w-7 rounded-full" />}

        {Array.isArray(futuresDaily) && futuresDaily.length !== 0 && (
          <>
            <Button 
              size="sm" 
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-blue-500 hover:bg-blue-600 text-white"
              onClick={() => setisOpened(!isOpened)}
            >
              {loading ? 'yükleniyor..' : isOpened ? 'Gizle' : 'Göster'}
            </Button>

            <Button 
              size="sm" 
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => {
                setselectedPairs(Favoriler);
                mode && props.callbackFN({ fname: 'pairs', fvar: Favoriler });
                mode && Array.isArray(Favoriler) && setlastpair(Favoriler.slice(-1).pop());
              }}
            >
              Favoriler
            </Button>

            <Button 
              size="sm" 
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => {
                let px = ["ADAUSDT", "BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT"];
                setselectedPairs(px);
                mode && props.callbackFN({ fname: 'pairs', fvar: px });
                mode && Array.isArray(px) && setlastpair(px.slice(-1).pop());
              }}
            >
              Rx
            </Button>

            <Button 
              size="sm" 
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => setRandomPairs(2)}
            >
              R2
            </Button>

            <Button 
              size="sm" 
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => setRandomPairs(5)}
            >
              R5
            </Button>

            <Button 
              size="sm" 
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => setRandomPairs(15)}
            >
              R15
            </Button>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1">
                    <Input
                      id="minVolume"
                      type="number"
                      placeholder="0"
                      value={minVolume}
                      onChange={fnSelectMinVolume}
                      className="h-7 w-16 text-xs px-2"
                    />
                    <Button 
                      size="sm" 
                      variant="default"
                      className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black h-7"
                      onClick={fnSetMinVolumeFilter}
                    >
                      set
                    </Button>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Set Minimum Volume For Selection (xMillion)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Button 
              size="sm" 
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={selectA}
            >
              Hepsi
            </Button>

            <Button 
              size="sm" 
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => {
                mode && props.callbackFN({ fname: 'pairs', fvar: [] });
                mode && setlastpair(null);
                setselectedPairs([]);
              }}
            >
              Hiçbirisi
            </Button>

            <Badge variant="default" className="h-7 px-2 text-xs rounded-md bg-cyan-400 text-black">
              Selected: #{selectedCount}
            </Badge>

            <Button 
              size="sm" 
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-orange-500 hover:bg-orange-600 text-black"
              onClick={openlink}
            >
              TV
            </Button>
          </>
        )}
      </div>

      {isOpened && (
        <div className="w-full overflow-auto max-h-[300px] mt-2">
          <div className="flex flex-wrap gap-1">
            {Array.isArray(futuresDaily) && futuresDaily.map(tag => {
              const isSelected = selectedPairs.includes(tag.s);
              return (
                <TooltipProvider key={tag.s}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => handleChange(tag.s, !isSelected)}
                        className={`px-2 py-1 text-xs rounded-sm border transition-colors ${
                          isSelected
                            ? 'bg-primary text-primary-foreground border-primary'
                            : 'bg-background text-foreground border-border hover:bg-accent'
                        }`}
                      >
                        {tag.s}
                      </button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Hacim: {tag.v}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              );
            })}
            <text className="text-xs text-muted-foreground">
              {futuresDaily.length} pairs found.
            </text>
          </div>
        </div>
      )}
    </div>
  );
};


// Pairs