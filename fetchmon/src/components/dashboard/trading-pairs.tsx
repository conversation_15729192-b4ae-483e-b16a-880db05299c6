/* eslint-disable @typescript-eslint/no-unused-expressions */
import { apiClient } from '@/lib/api';
import { TradingPairsApiResponse, TradingPair, PairData } from '@/lib/types';

// Trading pairs component

import { useState, useMemo, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Play,
  Pause,
  Search,
  AlertCircle,
  CheckCircle,
  Clock,
  MinusCircle
} from 'lucide-react';
import { SystemStatus } from '@/lib/types';

import { useServerStatus } from '@/context/ServerStatusContext';

interface TradingPairsProps {
  status: SystemStatus | null;
  loading: boolean;
  onStartPair: (pair: string) => void;
  onStopPair: (pair: string) => void;
}

// Define the status type for pairs
type PairStatus = 'active' | 'warning' | 'error' | 'stopped';

export function TradingPairs({ status, loading, onStartPair, onStopPair }: TradingPairsProps) {
  
  const { serverStatus, serverStatusLoading, loadingMessage, pingServer, startStopServer } = useServerStatus();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<PairStatus | 'all'>('all');
  const [sortColumn, setSortColumn] = useState<keyof TradingPair | null>('symbol');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Extract trading pairs from status
  const [tradingPairsData, setTradingPairsData] = useState<TradingPairsApiResponse | null>(null);
  const [pairsLoading, setPairsLoading] = useState(false);

  useEffect(() => {
    const fetchPairs = async () => {
      try {
        setPairsLoading(true);
        const data = await apiClient.getTradingPairsData();
        setTradingPairsData(data);
        console.log("Trading pairs data:", data);
      } catch (error) {
        console.error("Failed to fetch trading pairs data:", error);
      } finally {
        setPairsLoading(false);
      }
    };

    serverStatus === 'healthy' && fetchPairs();
    // const interval = setInterval(fetchPairs, 60000); // Refresh every 5 seconds
    // return () => clearInterval(interval);
  }, [serverStatus]);

  const tradingPairs = useMemo(() => {
    if (!tradingPairsData || !status?.system?.tickCollector) return [];

    return Object.entries(tradingPairsData).map(([symbol, data]) => {
      const connectionStatus = Object.values(status.system.tickCollector).find(
        (conn) => conn.url.includes(symbol)
      );

      let pairStatus: PairStatus = 'stopped';
      if (connectionStatus?.isConnected) {
        pairStatus = 'active';
      } else if (connectionStatus && connectionStatus.reconnectAttempts > 0) {
        pairStatus = 'warning'; // Or 'error' based on severity
      }

      return {
        symbol: symbol,
        status: pairStatus,
        lastUpdate: data.lastRecordTime,
        errorCount: 0, // API does not provide error count directly, assuming 0 for now
        recordCount: data.totalRecords,
        closedKlineCount: data.closedKlineCount,
        lastTimestamp: data.lastTimestamp
      } as TradingPair;
    });
  }, [tradingPairsData, status]);
  
  // Filter pairs based on search term and status filter
  const filteredPairs = useMemo(() => {
    let filtered = [...tradingPairs];

    if (statusFilter !== 'all') {
      filtered = filtered.filter(pair => pair.status === statusFilter);
    }

    filtered = filtered.filter(pair =>
      pair.symbol.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (sortColumn) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = a[sortColumn];
        const bValue = b[sortColumn];

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        } else if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortDirection === 'asc' ? (aValue) - (bValue) : (bValue) - (aValue);
        } else {
          return 0;
        }
      });
    }

    return filtered;
  }, [tradingPairs, searchTerm, statusFilter, sortColumn, sortDirection]);
  
  // Get status icon and color
  const getStatusIcon = (status: PairStatus) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'stopped':
        return <MinusCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };
  
  const getStatusColor = (status: PairStatus) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Format time since last update
  const formatTimeSince = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  const handleSort = (column: keyof TradingPair) => {
    if (column === sortColumn) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Loading skeleton
  if (loading || (pairsLoading && tradingPairsData === null)) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Trading Pairs - {serverStatus}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <div className="h-9 w-full bg-gray-200 rounded-md animate-pulse pl-8" />
              </div>
              <div className="h-9 w-32 bg-gray-200 rounded-md animate-pulse" />
            </div>
            
            <div className="rounded-md border">
              <div className="h-10 bg-gray-100 border-b" />
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 border-b last:border-b-0 flex items-center px-4">
                  <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Trading Pairs - {serverStatus}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search and Filter */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search pairs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as PairStatus | 'all')}
                className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="warning">Warning</option>
                <option value="error">Error</option>
                <option value="stopped">Stopped</option>
              </select>
            </div>
          </div>
          
          {/* Trading Pairs Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <button onClick={() => handleSort('symbol')} style={{ cursor: 'pointer' }}>
                      Symbol {sortColumn === 'symbol' && (sortDirection === 'asc' ? '▲' : '▼')}
                    </button>
                  </TableHead>
                  <TableHead>
                    <button onClick={() => handleSort('status')} style={{ cursor: 'pointer' }}>
                      Status {sortColumn === 'status' && (sortDirection === 'asc' ? '▲' : '▼')}
                    </button>
                  </TableHead>
                  <TableHead>
                    <button onClick={() => handleSort('lastUpdate')} style={{ cursor: 'pointer' }}>
                      Last Update {sortColumn === 'lastUpdate' && (sortDirection === 'asc' ? '▲' : '▼')}
                    </button>
                  </TableHead>
                  <TableHead>
                    <button onClick={() => handleSort('recordCount')} style={{ cursor: 'pointer' }}>
                      Records {sortColumn === 'recordCount' && (sortDirection === 'asc' ? '▲' : '▼')}
                    </button>
                  </TableHead>
                  <TableHead>
                    <button onClick={() => handleSort('closedKlineCount')} style={{ cursor: 'pointer' }}>
                      Closed Klines {sortColumn === 'closedKlineCount' && (sortDirection === 'asc' ? '▲' : '▼')}
                    </button>
                  </TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPairs.length > 0 ? (
                  filteredPairs.map((pair) => (
                    <TableRow key={pair.symbol}>
                      <TableCell className="font-medium uppercase">
                        {pair.symbol}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(pair.status)}
                          <Badge className={getStatusColor(pair.status)}>
                            {pair.status.charAt(0).toUpperCase() + pair.status.slice(1)}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        {formatTimeSince(pair.lastUpdate)}
                      </TableCell>
                      <TableCell>
                        {pair.recordCount.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {pair.closedKlineCount.toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {pair.status === 'stopped' ? (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onStartPair(pair.symbol)}
                            >
                              <Play className="h-4 w-4 mr-1" />
                              Start
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => onStopPair(pair.symbol)}
                            >
                              <Pause className="h-4 w-4 mr-1" />
                              Stop
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No pairs found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          
          {/* Bulk Actions */}
          <div className="flex justify-end gap-2">
            <Button variant="outline">
              Start All
            </Button>
            <Button variant="outline">
              Stop All
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
