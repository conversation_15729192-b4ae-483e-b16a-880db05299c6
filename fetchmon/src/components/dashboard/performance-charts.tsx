// Performance charts component

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { 
  <PERSON><PERSON>hart, 
  Line, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend
} from 'recharts';
import { MetricsData } from '@/lib/types';
import { useState, useEffect } from 'react';

interface PerformanceChartsProps {
  metrics: MetricsData | null;
  loading: boolean;
}

// Data structure for chart points
interface MemoryDataPoint {
  time: string;
  memory: number;
}

interface QueueDataPoint {
  name: string;
  value: number;
}

export function PerformanceCharts({ metrics, loading }: PerformanceChartsProps) {
  // State for memory usage over time
  const [memoryData, setMemoryData] = useState<MemoryDataPoint[]>([]);
  
  // Update memory data when metrics change
  useEffect(() => {
    if (metrics) {
      const newPoint: MemoryDataPoint = {
        time: new Date().toLocaleTimeString(),
        memory: Math.round((metrics.processMemory.heapUsed / 1024 / 1024) * 100) / 100
      };
      
      setMemoryData(prev => {
        const newData = [...prev, newPoint];
        // Keep only the last 20 data points
        return newData.length > 20 ? newData.slice(-20) : newData;
      });
    }
  }, [metrics]);
  
  // Prepare queue data for bar chart
  const queueData: QueueDataPoint[] = metrics
    ? [
        { name: 'Waiting', value: metrics.tickQueueJobs.waiting },
        { name: 'Active', value: metrics.tickQueueJobs.active },
        { name: 'Completed', value: metrics.tickQueueJobs.completed },
        { name: 'Failed', value: metrics.tickQueueJobs.failed },
        { name: 'Delayed', value: metrics.tickQueueJobs.delayed }
      ]
    : [];

  // Loading skeleton for charts
  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Memory Usage Over Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80 flex items-center justify-center">
              <div className="h-64 w-full bg-gray-200 rounded animate-pulse" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Queue Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80 flex items-center justify-center">
              <div className="h-64 w-full bg-gray-200 rounded animate-pulse" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2">
      {/* Memory Usage Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Memory Usage Over Time</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={memoryData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="time" 
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => {
                    // Show only time part, not full date
                    return value.split(':').slice(0, 2).join(':');
                  }}
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `${value} MB`}
                />
                <Tooltip 
                  formatter={(value) => [`${value} MB`, 'Memory']}
                  labelFormatter={(label) => `Time: ${label}`}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="memory"
                  stroke="#8884d8"
                  activeDot={{ r: 8 }}
                  name="Memory (MB)"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Queue Statistics Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Queue Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={queueData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip 
                  formatter={(value) => [value, 'Jobs']}
                />
                <Legend />
                <Bar 
                  dataKey="value" 
                  name="Jobs Count" 
                  fill="#82ca9d" 
                  strokeWidth={2}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
