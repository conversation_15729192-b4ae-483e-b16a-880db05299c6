'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Modal } from '@/components/ui/modal';
import { Settings, Edit } from 'lucide-react';

interface WebSocketConfig {
  pairsPerConnection: number;
  healthCheckInterval: number;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  klineInterval: string;
}

interface ConfigData {
  websocket: WebSocketConfig;
  pairs2Collect: string[];
  klineIntervals: string[];
}

interface EditConfigData {
  websocket: WebSocketConfig;
  pairs2Collect: string[];
  klineIntervals: string[];
}

const klineIntervalOptions = [
  "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h",
  "6h", "8h", "12h", "1d", "3d", "1w", "1M"
];

export function SystemConfig() {
  const [config, setConfig] = useState<ConfigData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editConfig, setEditConfig] = useState<EditConfigData | null>(null);
  const [availableSymbols, setAvailableSymbols] = useState<string[]>([]);
  const [symbolsLoading, setSymbolsLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  useEffect(() => {
    fetchConfig();
  }, []);

  const fetchConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3001/api/server/config');
      if (!response.ok) {
        throw new Error('Failed to fetch config');
      }
      const data = await response.json();
      setConfig(data.config);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableSymbols = async () => {
    try {
      setSymbolsLoading(true);
      const response = await fetch('http://localhost:3001/api/server/dims/symbols');
      if (!response.ok) {
        throw new Error('Failed to fetch available symbols');
      }
      const data = await response.json();
      setAvailableSymbols(data.symbols || []);
    } catch (err) {
      console.error('Error fetching symbols:', err);
    } finally {
      setSymbolsLoading(false);
    }
  };

  const handleEdit = () => {
    if (config) {
      setEditConfig(JSON.parse(JSON.stringify(config))); // Deep copy
      setIsEditing(true);
      fetchAvailableSymbols();
    }
  };

  const handleSave = async () => {
    if (!editConfig) return;
    
    try {
      setSaving(true);
      setSaveError(null);
      
      // Send updated config to the API
      const response = await fetch('http://localhost:3001/api/server/config/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editConfig),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save configuration');
      }
      
      const result = await response.json();
      setConfig(editConfig);
      setIsEditing(false);
    } catch (err) {
      setSaveError(err instanceof Error ? err.message : 'Failed to save configuration');
    } finally {
      setSaving(false);
    }
  };

  const updateWebsocketConfig = (field: keyof WebSocketConfig, value: string | number) => {
    if (!editConfig) return;
    
    setEditConfig({
      ...editConfig,
      websocket: {
        ...editConfig.websocket,
        [field]: value
      }
    });
  };

  const addPair = () => {
    if (!editConfig) return;
    
    setEditConfig({
      ...editConfig,
      pairs2Collect: [...editConfig.pairs2Collect, '']
    });
  };

  const removePair = (index: number) => {
    if (!editConfig) return;
    
    const newPairs = [...editConfig.pairs2Collect];
    newPairs.splice(index, 1);
    
    setEditConfig({
      ...editConfig,
      pairs2Collect: newPairs
    });
  };

  const updatePair = (index: number, value: string) => {
    if (!editConfig) return;
    
    const newPairs = [...editConfig.pairs2Collect];
    newPairs[index] = value.toLowerCase();
    
    setEditConfig({
      ...editConfig,
      pairs2Collect: newPairs
    });
  };

  const addKlineInterval = () => {
    if (!editConfig) return;
    
    setEditConfig({
      ...editConfig,
      klineIntervals: [...editConfig.klineIntervals, '1m']
    });
  };

  const removeKlineInterval = (index: number) => {
    if (!editConfig) return;
    
    const newIntervals = [...editConfig.klineIntervals];
    newIntervals.splice(index, 1);
    
    setEditConfig({
      ...editConfig,
      klineIntervals: newIntervals
    });
  };

  const updateKlineInterval = (index: number, value: string) => {
    if (!editConfig) return;
    
    const newIntervals = [...editConfig.klineIntervals];
    newIntervals[index] = value;
    
    setEditConfig({
      ...editConfig,
      klineIntervals: newIntervals
    });
  };

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-red-500">Error loading config: {error}</div>
          <Button onClick={fetchConfig} className="mt-2">Retry</Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              System Configuration
            </div>
            <Button variant="outline" size="sm" onClick={handleEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-full" />
            </div>
          ) : config ? (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">WebSocket Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Pairs per Connection:</span>
                    <span>{config.websocket.pairsPerConnection}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Health Check Interval:</span>
                    <span>{config.websocket.healthCheckInterval}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Reconnect Interval:</span>
                    <span>{config.websocket.reconnectInterval}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Max Reconnect Attempts:</span>
                    <span>{config.websocket.maxReconnectAttempts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Kline Interval:</span>
                    <span>{config.websocket.klineInterval}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Pairs to Collect</h3>
                <div className="flex flex-wrap gap-2">
                  {config.pairs2Collect.map((pair, index) => (
                    <Badge key={index} variant="secondary">{pair.toUpperCase()}</Badge>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Kline Intervals</h3>
                <div className="flex flex-wrap gap-2">
                  {config.klineIntervals.map((interval, index) => (
                    <Badge key={index} variant="secondary">{interval}</Badge>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div>No configuration data available</div>
          )}
        </CardContent>
      </Card>

      <Modal 
        isOpen={isEditing} 
        onClose={() => setIsEditing(false)} 
        title="Edit System Configuration"
      >
        {editConfig && (
          <div className="space-y-6">
            {saveError && (
              <div className="text-red-500 text-sm">{saveError}</div>
            )}
            
            <div>
              <h3 className="font-medium mb-3">WebSocket Configuration</h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium">Pairs per Connection</label>
                  <Input
                    type="number"
                    value={editConfig.websocket.pairsPerConnection}
                    onChange={(e) => updateWebsocketConfig('pairsPerConnection', parseInt(e.target.value) || 0)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Health Check Interval (ms)</label>
                  <Input
                    type="number"
                    value={editConfig.websocket.healthCheckInterval}
                    onChange={(e) => updateWebsocketConfig('healthCheckInterval', parseInt(e.target.value) || 0)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Reconnect Interval (ms)</label>
                  <Input
                    type="number"
                    value={editConfig.websocket.reconnectInterval}
                    onChange={(e) => updateWebsocketConfig('reconnectInterval', parseInt(e.target.value) || 0)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Max Reconnect Attempts</label>
                  <Input
                    type="number"
                    value={editConfig.websocket.maxReconnectAttempts}
                    onChange={(e) => updateWebsocketConfig('maxReconnectAttempts', parseInt(e.target.value) || 0)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Kline Interval</label>
                  <Select
                    value={editConfig.websocket.klineInterval}
                    onValueChange={(value) => updateWebsocketConfig('klineInterval', value)}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {klineIntervalOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-medium">Pairs to Collect</h3>
                <Button variant="outline" size="sm" onClick={addPair}>
                  Add Pair
                </Button>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {editConfig.pairs2Collect.map((pair, index) => (
                  <div key={index} className="flex gap-2">
                    <Select
                      value={pair}
                      onValueChange={(value) => updatePair(index, value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select pair" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableSymbols
                          .filter(symbol => 
                            !editConfig.pairs2Collect.includes(symbol) || symbol === pair
                          )
                          .map((symbol) => (
                            <SelectItem key={symbol} value={symbol}>
                              {symbol.toUpperCase()}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => removePair(index)}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
                {editConfig.pairs2Collect.length === 0 && (
                  <p className="text-sm text-muted-foreground">No pairs configured</p>
                )}
              </div>
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-medium">Kline Intervals</h3>
                <Button variant="outline" size="sm" onClick={addKlineInterval}>
                  Add Interval
                </Button>
              </div>
              <div className="space-y-2">
                {editConfig.klineIntervals.map((interval, index) => (
                  <div key={index} className="flex gap-2">
                    <Select
                      value={interval}
                      onValueChange={(value) => updateKlineInterval(index, value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {klineIntervalOptions
                          .filter(option => 
                            !editConfig.klineIntervals.includes(option) || option === interval
                          )
                          .map((option) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => removeKlineInterval(index)}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
                {editConfig.klineIntervals.length === 0 && (
                  <p className="text-sm text-muted-foreground">No intervals configured</p>
                )}
              </div>
            </div>
            
            <div className="flex justify-end gap-2 pt-4">
              <Button 
                variant="outline" 
                onClick={() => setIsEditing(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
}
